package constmap

import (
	"regexp"
	"time"
)

type HttpMethod string

const (
	HttpMethodGet    = HttpMethod("GET")
	HttpMethodPost   = HttpMethod("POST")
	HttpMethodPut    = HttpMethod("PUT")
	HttpMethodDelete = HttpMethod("DELETE")
)

const (
	appCode          = "roadtrip"
	ContextSource    = "source"
	ContextUser      = "user"
	ContextHttpRaw   = "raw"
	ContextFrontUser = "front_user"
	Enable           = 1
	Disable          = 2
	Unknown          = 0
	UnknownStr       = "未知"
	DefaultPageSize  = 20
	DefaultCacheTime = time.Hour * 24 * 3
	DefaultZoneId    = 7 //默认城市：上海
	RobotUserId      = 10000
)

const QiniuPrefix = "yiban/static/resources" //七牛云存储前缀

type ResourcePath string

const PathPlanPost ResourcePath = "plan_poster" //行程海报
const WishMedia ResourcePath = "wish_media"

var StateMap = map[int]string{
	Enable:  "可用",
	Disable: "不可用",
}

type OrderType string
type OrderSubType int
type ThirdOrderType string
type PaymentType int
type ThirdPayState int

const (
	OrderTypeTuan   OrderType = "tuan"   //团游
	OrderTypeTicket OrderType = "ticket" //门票
	OrderTypeHotel  OrderType = "hotel"  //酒店
	OrderTypeTravel OrderType = "travel" //旅行打包订单

	OrderSubTypeTuan   OrderSubType = 1 //团游
	OrderSubTypeTicket OrderSubType = 2 //门票
	OrderSubTypeHotel  OrderSubType = 3 //酒店

	ThirdOrderTypeZwy    ThirdOrderType = "zwy"    //自我游订单
	ThirdOrderTypeCeekee ThirdOrderType = "ceekee" //思客

	PaymentTypeNormal  PaymentType = 10 //普通订单支付
	PaymentTypeVip     PaymentType = 20 //会员订单支付
	PaymentTypePackage PaymentType = 30 //套餐订单支付
	//订单状态
	OrderStateNotPay     = 1  //订单状态（未支付）
	OrderStatePayEarnest = 2  //订单状态（已付预付款）
	OrderStatePayed      = 3  //订单状态（已支付）
	OrderStateProcessing = 4  //订单状态（处理中）【非团游订单才用】
	OrderStateSuccess    = 5  //订单状态（处理成功）【非团游订单才用】
	OrderStateCancel     = 11 //订单状态（已取消）
	OrderStateRefund     = 12 //订单状态（已退款，全部子订单已退款）
	OrderStateComplete   = 13 //订单状态（已完成）

	//子订单状态
	OrderDetailStateWaitPay    = 5  //待支付
	OrderDetailStateProcessing = 10 //处理中(待确认)
	OrderDetailStateFail       = 20 //处理失败(确认失败)
	OrderDetailStateSuccess    = 30 //处理成功(已支付已确认)
	OrderDetailStateComplete   = 40 //完成(包含部分核销、部分退款)
	OrderDetailStateRefund     = 50 //退款

	ThirdPayStateNeedConfirm ThirdPayState = 10 //待确认
	ThirdPayStateConfirmFail ThirdPayState = 15 //确认失败
	ThirdPayStateWaitPay     ThirdPayState = 20 //待支付
	ThirdPayStateWaitPrint   ThirdPayState = 25 //待出票
	ThirdPayStatePrintFail   ThirdPayState = 26 //出票失败
	ThirdPayStatePayed       ThirdPayState = 30 //已确认、已支付(zwy:待确认-待支付-已支付 ck:待支付-待确认-已支付)
	ThirdPayStateFail        ThirdPayState = 40 //支付失败
	ThirdPayStateRefunding   ThirdPayState = 50 //退款处理中
	ThirdPayStateRefund      ThirdPayState = 60 //已退款
	ThirdPayStateRefundFail  ThirdPayState = 70 //退款异常

	PayStateNotPay  = 1 //支付状态（未支付）
	PayStatePayed   = 2 //支付状态（已支付）
	PayStatePartPay = 3 //支付状态（部分支付）

	PayMethodWeixin   = 1 //支付方式（公众号支付）
	PayMethodTransfer = 2 //支付方式（对公转账）
	PayMethodMpWeixin = 3 //支付方式（微信小程序）

	TimeLayoutWeixinPay = "2006-01-02T15:04:05-07:00" //微信支付的时间格式
)

type RefundOrderState int
type RefundSubOrderState int
type RefundSubPaymentState int

const (
	RefundStateDefault     = 1 //退款状态（默认）
	RefundStateApproved    = 2 //退款状态（审核通过）
	RefundStateIng         = 3 //退款状态（退款中）
	RefundStateComplete    = 4 //退款状态（退款完成）
	RefundStateAbnormal    = 5 //退款状态（退款异常）
	RefundStateThirdRefund = 6 //第三方OTA退款处理 (审核通过后包含OTA订单的退款会流转到此状态，由脚本发起OTA退款，再轮询OTA退款状态发起站内退款)

	RefundOrderWaitReview RefundOrderState = 1 //待审核
	RefundOrderProcessing RefundOrderState = 2 //未完成
	RefundOrderFinish     RefundOrderState = 3 //已完成

	RefundSubOrderWaitReview RefundSubOrderState = 1 //待审核
	RefundSubOrderReject     RefundSubOrderState = 2 //拒绝退款
	RefundSubOrderWaitRefund RefundSubOrderState = 3 //可退款
	RefundSubOrderRefunding  RefundSubOrderState = 4 //退款中
	RefundSubOrderRefunded   RefundSubOrderState = 5 //退款完成
	RefundSubOrderAbnormal   RefundSubOrderState = 6 //退款异常

	RefundSubPaymentWaitRefund RefundSubPaymentState = 1 //可退款
	RefundSubPaymentRefunding  RefundSubPaymentState = 2 //退款中
	RefundSubPaymentRefunded   RefundSubPaymentState = 3 //退款完成
	RefundSubPaymentAbnormal   RefundSubPaymentState = 4 //退款异常
)

const (
	PoiTypeScenic = 1 //poi类型（景点）
	PoiTypeHotel  = 2 //poi类型（酒店）
	PoiTypeZone   = 3 //poi类型（地区）
)

const (
	SourceMpWeixin = 1 // 渠道来源（微信小程序）
	SourceWeixinH5 = 2 //渠道来源（微信H5）
)

const (
	OauthTypeWeixinH5 = 2 //Oauth类型（微信H5）
	OauthTypeMpWeixin = 1 //Oauth类型（微信小程序）
)

const (
	OtaCodeAmap      = "amap"   //Ota代码（高德）
	OtaCodeZwy       = "zwy"    //Ota代码 (自我游)
	OtaCodeCeekee    = "ceekee" //Ota代码 (思客)
	OtaCodeYb        = "yb"     //Ota代码 (义伴)
	OtaCodeTongCheng = "tc"     //Ota代码 (同程)
)

var OtaCodeMap = map[string]string{
	OtaCodeAmap:      "高德",
	OtaCodeZwy:       "自我游",
	OtaCodeCeekee:    "思客",
	OtaCodeTongCheng: "同程",
}

const (
	ZoneLevelDistrict = 4
	ZoneLevelCity     = 3
	ZoneLevelProvince = 2
)

const (
	GotStateDefault = 1 //领取状态（未领取）
	GotStated       = 2 //领取状态（已领取）
	GotStateExpired = 3 //领取状态（已过期）

	FlowCardTypeDefault = 1 //流量卡类型（通用码）
	FlowCardTypeUnique  = 2 //流量卡类型（唯一码）
)

const (
	GetMethodType = 1 //领取类型（站内领取）
)

const EsIndexPoi = "poi"   //es索引（景点、酒店、城市）
const EsIndexTuan = "tuan" //团游
const EsIndexWish = "wish" //心愿单

type TravelInterest string      //旅行偏好
type TravelStrength string      //旅行强度
type TravelAccommodation string //旅行住宿
type TravelHowPlay string       //如何游玩
const (
	TravelInterestNature        TravelInterest = "1" //旅行偏好（自然）
	TravelInterestHumanity      TravelInterest = "2" //旅行偏好（人文）
	TravelInterestChildren      TravelInterest = "3" //旅行偏好（亲子）
	TravelInterestEntertainment TravelInterest = "4" //旅行偏好（娱乐）

	TravelStrengthRelaxed  TravelStrength = "1" //旅行强度（轻松）
	TravelStrengthModerate TravelStrength = "2" //旅行强度（适中）
	TravelStrengthCompact  TravelStrength = "3" //旅行强度（紧凑）

	TravelAccommodationEconomic TravelAccommodation = "1" //旅行住宿（经济）
	TravelAccommodationModerate TravelAccommodation = "2" //旅行住宿（舒适）
	TravelAccommodationLuxury   TravelAccommodation = "3" //旅行住宿（豪华）

	TravelHowPlayDestination TravelHowPlay = "1" //目的地游玩
	TravelHowPlayRouting     TravelHowPlay = "2" //一路玩
)

type VoucherState int

const (
	VoucherStateNormal    VoucherState = 1 //普通
	VoucherStateUsed      VoucherState = 2 //已核销
	VoucherStateCanceling VoucherState = 3 //作废处理
	VoucherStateCancel    VoucherState = 4 //已作废‘
)

type AiType int

const (
	AiTypeDoubao AiType = 1 //AI类型（豆包）
)

const (
	TimeDur5s  = time.Second * 5
	TimeDur30s = time.Second * 30
	TimeDur1m  = time.Minute
	TimeDur5m  = time.Minute * 5
	TimeDur10m = time.Minute * 10
	TimeDur1h  = time.Hour
	TimeDur1d  = 24 * time.Hour
	TimeDur7d  = 7 * 24 * time.Hour
	TimeDur15d = 15 * 24 * time.Hour
	TimeDur30d = 30 * 24 * time.Hour
	TimeDur40d = 40 * 24 * time.Hour
	TimeDur90d = 90 * 24 * time.Hour
	TimeSec5s  = 5
	TimeSec1h  = 3600
	TimeSec1d  = 86400
	TimeSec7d  = 7 * TimeSec1d
)

var (
	RegexSymbolExp     = regexp.MustCompile("[，,、：:]")
	RegexCitySuffix    = regexp.MustCompile("(市|省|自治区|自治州)$")
	RegexCitySuffixAll = regexp.MustCompile("(市|省|自治区|自治州)*")
	RegexDateFmtLong   = regexp.MustCompile(`\d{4}-\d{2}-\d{2}`)
)

type ProductType string //产品类型
const (
	ProductTypeTuan      ProductType = "tuan"    //团游
	ProductTypeZwyTicket ProductType = "zticket" //自我游门票
	ProductTypeCkHotel   ProductType = "ckhotel" //思客酒店
)

type IdType int //证件类型
const (
	IdTypeIdCard           IdType = 1 //身份证
	IdTypeStudent          IdType = 2 //学生证
	IdTypeSoldier          IdType = 3 //军官证
	IdTypePassport         IdType = 4 //护照
	IdTypeResidenceBooklet IdType = 5 //户口本
	IdTypeGAPassport       IdType = 6 //港澳通行证
	IdTypeTaiwanResident   IdType = 7 //台胞证
	IdTypeTaiwanPassport   IdType = 8 //台湾通行证
	IdTypeGreenCard        IdType = 9 //外国人在中国永久居留证
)

var ValidIdType = []IdType{IdTypeIdCard, IdTypePassport, IdTypeSoldier, IdTypeTaiwanResident, IdTypeGAPassport, IdTypeTaiwanPassport, IdTypeGreenCard}

type DateUnixStamp struct{ time.Time }  //11位unix时间戳
type DateTimeMinute struct{ time.Time } //2024-01-02 18:00
type DateTime struct{ time.Time }       //2024-01-02 18:00:00
type Date struct{ time.Time }           //2024-01-02

const (
	DateFmtShortFullShortYear = "060102150405"
	DateFmtShortFull          = "20060102150405"
	DateFmtShort              = "20060102"
	DateFmtLongFull           = "2006-01-02 15:04:05"
	DateFmtLong               = "2006-01-02"
	DateFmtLongDashed         = "2006/01/02"
	DateFmtLongMinute         = "2006-01-02 15:04"
	DateFmtCnMonDay           = "1月2日"
	DateFmtCnLong             = "2006年1月2日"
	DateFmtMonthFull          = "2006-01"
)

type UserType int
type VipBenefitCode string //Vip权益
type VipBenefitType int
type GuestType int //客人类型

const (
	UserTypeNormal UserType = 10 //普通用户
	UserTypeVip    UserType = 20 //VIP用户

	VipBenefitTravel  VipBenefitCode = "travel"   //目的地旅游折扣
	VipBenefitWifi    VipBenefitCode = "wifi"     //机上wifi
	VipBenefitPickUp  VipBenefitCode = "pick_up"  //接送机
	VipBenefitRentCar VipBenefitCode = "rent_car" //租车
	VipBenefitAct     VipBenefitCode = "act"      //线下自驾主题活动

	VipBenefitTypeDiscount VipBenefitType = 1 //折扣
	VipBenefitTypeTimes    VipBenefitType = 2 //次数

	GuestTypeMainland GuestType = 1 //仅大陆客人
	GuestTypeOverseas GuestType = 2 //仅大陆和港澳台
	GuestTypeAll      GuestType = 3 //大陆、港澳台及外国人

)

type HotelCancelPolicyType int //取消订单政策类型

const (
	HotelCancelPolicyFree HotelCancelPolicyType = 1 + iota
	HotelCancelPolicyFixedMoney
	HotelCancelPolicyPercent
	HotelCancelPolicyFirstNight
	HotelCancelPolicyAll
)

type JTimelineType string

const (
	JTimelineScene JTimelineType = "scene" //景点
	JTimelineText  JTimelineType = "text"  //普通文字
	JTimelineHotel JTimelineType = "hotel" //酒店
	JTimelinePlane JTimelineType = "plane" //飞机
	JTimelineTrain JTimelineType = "train" //火车
	JTimelineBus   JTimelineType = "bus"   //大巴
)

type BannerPosition int

const (
	BannerPositionHome       BannerPosition = 1 + iota //首页
	BannerPositionFaXian                               //发现
	BannerPositionWishSquare                           //心愿广场
)

type TaskInterval int
type TaskReward int //任务奖励类型
type TaskCond int   //任务奖励条件
type TaskRelateType int
type UserTaskState int       //用户任务状态
type UserTaskLogType int     //用户任务日志类型
type UserTaskRewardState int //用户任务奖励状态
const (
	TaskIntervalOnce TaskInterval = 1 //一次性任务
	TaskIntervalDay  TaskInterval = 2 //日任务
	TaskIntervalWeek TaskInterval = 3 //周任务

	TaskRewardActIntegral     TaskReward = 1 //活动积分
	TaskRewardAirWifi         TaskReward = 2 //机上wifi
	TaskRewardRealProduct     TaskReward = 3 //实物奖励
	TaskRewardAccountIntegral TaskReward = 4 //账户积分

	TaskCondTaskAccIntegral TaskCond = 2  //任务累计积分达到数额
	TaskCondSharePlan       TaskCond = 3  //分享行程(mq)
	TaskCondSavePlan        TaskCond = 4  //收藏行程(mq)
	TaskCondMakePlan        TaskCond = 5  //行程规划(mq)
	TaskCondJoinAct         TaskCond = 6  //参与活动(back)
	TaskCondShareAct        TaskCond = 7  //分享活动并登录(mq)
	TaskCondSceneSign       TaskCond = 8  //景点打卡
	TaskCondSceneSignAll    TaskCond = 9  //完成所有景点打卡
	TaskCondShareMini       TaskCond = 10 //分享小程序并登录(mq)
	TaskCondRankSettle      TaskCond = 11 //排行榜结算入榜
	TaskCondParent          TaskCond = 12 //完成父级任务
	TaskCondPlanLike        TaskCond = 13 //行程点赞

	UserTaskProcessing UserTaskState = 1 //进行中
	UserTaskWaitReward UserTaskState = 2 //待领取奖励
	UserTaskDone       UserTaskState = 3 //已完成

	UserTaskLogDo     UserTaskLogType = 1 //做任务
	UserTaskLogCond   UserTaskLogType = 2 //达成条件
	UserTaskLogReward UserTaskLogType = 3 //领取奖励

	UserTaskRewardWait UserTaskRewardState = 1 //待发货
	UserTaskRewardSelf UserTaskRewardState = 2 //已自提
	UserTaskRewardSend UserTaskRewardState = 3 //已发货

	TaskRelateTuan     TaskRelateType = 1 //团游
	TaskRelateActivity TaskRelateType = 2 //活动
)

type CurrencyType int
type AccountLog int    //日志类型
type AccountLogSub int //日志子类型
const (
	CurrencyIntegral CurrencyType = 1 //积分

	AccountLogIncrAmount      AccountLog = 1 //增加
	AccountLogDecrAmount      AccountLog = 2 //减少
	AccountLogIncrLockAmount  AccountLog = 3 //增加冻结
	AccountLogDecrLockAmount  AccountLog = 4 //减少冻结
	AccountLogConvAmount2Lock AccountLog = 5 //余额转冻结
	AccountLogConvLock2Amount AccountLog = 6 //冻结转余额

	AccountLogSubBuyPackage     AccountLogSub = 1  //购买套餐
	AccountLogSubInitUser       AccountLogSub = 2  //初始化用户
	AccountLogSubBindInviteCode AccountLogSub = 3  //绑定邀请码
	AccountLogSubPlanCost       AccountLogSub = 4  //生成行程扣减
	AccountLogSubPlanPdf        AccountLogSub = 5  //生成PDF消耗积分数
	AccountLogSubShareMini      AccountLogSub = 6  //分享小程序
	AccountLogSubPackageExpire  AccountLogSub = 7  //套餐到期清零
	AccountLogSubAdminOperate   AccountLogSub = 8  //管理员操作
	AccountLogSubAdView         AccountLogSub = 9  //看广告得积分
	AccountLogSubActReward      AccountLogSub = 10 //活动奖励
)

type PlanPdfState int

const (
	PlanPdfStateNone PlanPdfState = 0 //行程的PDF生成状态（无）
	PlanPdfStateIng  PlanPdfState = 1 //行程的PDF生成状态（进行中）
	PlanPdfStateDone PlanPdfState = 2 //行程的PDF生成状态（完成）
	PlanPdfStateFail PlanPdfState = 3 //行程的PDF生成状态（失败）
)

type PlanCostDetailState int

const (
	PlanCostDetailStateNoStart PlanCostDetailState = 1 //预算明细任务(未开始)
	PlanCostDetailStateRunning PlanCostDetailState = 2 //预算明细任务(进行中)
	PlanCostDetailStateDone    PlanCostDetailState = 3 //预算明细任务(完成)
)

type SysConfigValueType int

const (
	SysConfigTypeText SysConfigValueType = 1 + iota
	SysConfigTypeNumber
	SysConfigTypeJson
	SysConfigTypeBool
	SysConfigTypeUrl
	SysConfigTypeImage

	SysConfigDifyKnowledgeHotelKey     = "difyKnowledgeHotelKey" //酒店知识库apiKey
	SysConfigDifyHotelRecReasonKey     = "difyHotelRecReasonKey" //dify:酒店推荐理由
	SysConfigDifyChatKey               = "difyChatKey"           //dify:对话流
	SysConfigDifyHotelChatKey          = "difyHotelChatKey"      //dify:酒店推荐
	SysConfigDifySpiderSceneKey        = "difySpiderSceneKey"    //dify:获取景点
	SysConfigDifyRobotPlanKey          = "difyRobotPlanKey"      //dify:机器人行程规划
	SysConfigDifyRecPromptsKey         = "difyRecPromptsKey"     //dify:推荐语
	SysConfigDifyPlanPdfKey            = "difyPlanPdfKey"        //dify:生成行程PDF
	SysConfigDifyPlanHotTagsKey        = "difyPlanHotTagsKey"    //dify:获取当月行程HotTagsev
	SysConfigDifyZoneSpecialityKey     = "difyZoneSpecialityKey" //dify:获取城市美食、特产、民俗文化
	SysConfigDifyPlanTagsKey           = "difyPlanTagsKey"       //dify:获取行程标签
	SysConfigDifyMakeWishPlanKey       = "difyMakeWishPlanKey"   //dify:生成心愿单行程
	SysConfigDifyHotZonesKey           = "difyHotZonesKey"       //dify:热门城市
	SysConfigDifyWishTodosKey          = "difyWishTodosKey"      //dify:心愿单想做的事
	SysConfigDifyWishSubjectKey        = "difyWishSubjectKey"    //dify:心愿单主题
	SysConfigDifyWishDescKey           = "difyWishDescKey"       //dify:心愿单描述
	SysConfigPlanCostAmount            = "planCostAmount"        //行程规划消耗积分数
	SysConfigReplanCostAmount          = "replanCostAmount"      //AI智能重规划消耗积分数
	SysConfigBindInviteCodeAmount      = "BindInviteCodeAmount"  //绑定邀请码获取的积分数
	SysConfigInitIntegralAmount        = "initIntegralAmount"    //初始用户积分数
	SysConfigGeneratePlanPdfCostAmount = "planPdfCostAmount"     //生成PDF消耗积分数
	SysConfigTplMsgKeys                = "tplMsgKeys"            //模板消息key
	SysConfigAdConfig                  = "adConfig"              //广告配置
)

type UserPackageState int
type PackageExpUnit int //过期时间单位
const (
	UserPackageStateDefault  UserPackageState = 1 //未生效
	UserPackageStateUsing    UserPackageState = 2 //使用中
	UserPackageStateComplete UserPackageState = 3 //已使用
	UserPackageStateExpired  UserPackageState = 4 //过期处理
	UserPackageStateAbnormal UserPackageState = 5 //异常处理

	PackageExpUnitWeek   PackageExpUnit = 1 //周
	PackageExpUnitMonth  PackageExpUnit = 2 //月
	PackageExpUnitSeason PackageExpUnit = 3 //季度
	PackageExpUnitYear   PackageExpUnit = 4 //年
)

type TransactionState int

const (
	TransactionStateInit    TransactionState = 1 //初始化:处理中
	TransactionStateSuccess TransactionState = 2 //成功
	TransactionStateFail    TransactionState = 3 //失败
)
