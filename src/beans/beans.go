package beans

import (
	"encoding/json"
	"fmt"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/utils"
	"strings"
	"time"
)

type AuthTokenInfo struct {
	Name   string `json:"name"`
	Token  string `json:"token"`
	Avatar string `json:"avatar"`
}

type PayStr struct {
	PayStr string `json:"pay_str"`
	PayId  uint   `json:"pay_id"`
}

type TravelTmp struct {
	TmpId          string `json:"tmp_id"`
	FromLocation   string `json:"from_location"`
	ToLocation     string `json:"to_location"`
	FromCityZoneId uint   `json:"from_city_zone_id"`
	ToCityZoneId   uint   `json:"to_city_zone_id"`
	FromCity       string `json:"from_city"`
	ToCity         string `json:"to_city"`
	Cities         []uint `json:"cities"`
	Spots          []uint `json:"spots"`
}

func (t *TravelTmp) MarshalBinary() (data []byte, err error) {
	return json.Marshal(t)
}

func (t *TravelTmp) UnmarshalBinary(data []byte) error {
	return json.Unmarshal(data, t)
}

type JourneyPoi struct {
	Id       uint    `json:"id"`
	Pic      string  `json:"pic"`
	Name     string  `json:"name"`
	Distance float64 `json:"distance"`
	Type     int     `json:"type"`
	Lng      float64 `json:"lng"`
	Lat      float64 `json:"lat"`
	Price    string  `json:"price"`
}

type JTimeline struct {
	Pics      []string               `json:"pics"`
	Title     string                 `json:"title"`
	Type      constmap.JTimelineType `json:"type"`
	Time      string                 `json:"time"` //上午、中午、晚上
	Desc      string                 `json:"desc"`
	ItemId    uint                   `json:"item_id"` //酒店、景点ID
	Cate      []string               `json:"cate"`    //分类
	Tags      []string               `json:"tags"`
	AvgPrice  int64                  `json:"avg_price"`
	Scene     *JTimelineScene        `json:"scene"`
	Poi       *JourneyPoi            `json:"poi,omitempty"`
	Transport *JTimelineTransport    `json:"transport,omitempty"` //交通信息
}

type JTimelineScene struct {
	IsFree   bool            `json:"is_free"`
	CostTime ScenicPlayCosts `json:"cost_time"`
	Prices   ScenicPrices    `json:"prices"`
}

type JTimelineTransport struct {
	From JTimelineTransportPoi `json:"from"`
	To   JTimelineTransportPoi `json:"to"`
}

type JTimelineTransportPoi struct {
	ZoneId   uint    `json:"zone_id"`
	ZoneName string  `json:"zone_name,omitempty"`
	Station  string  `json:"station"`
	Lng      float64 `json:"lng"`
	Lat      float64 `json:"lat"`
}

// 一天行程
type Journey struct {
	Title       string                 `json:"title"`
	Subject     string                 `json:"subject"`
	Content     string                 `json:"content"`      //【废弃】
	Zones       string                 `json:"zones"`        //途径城市
	Scenic      string                 `json:"scenic"`       //【废弃】 途径景点
	Speciality  string                 `json:"speciality"`   //途径特产
	HotelConds  *HotelSearchConditions `json:"hotel_conds"`  //酒店搜索条件
	WayPoints   []*JourneyPoi          `json:"way_points"`   //城市
	ScenicSpots []*JourneyPoi          `json:"scenic_spots"` //景点
	Hotels      []*JourneyPoi          `json:"hotels"`       //酒店
	Timeline    []*JTimeline           `json:"timeline"`     //行程时间轴
}

type RenderTravelJourney struct {
	Title        string     `json:"title"`
	Subtitle     string     `json:"subtitle"`
	Notice       string     `json:"notice"`        //注意事项
	FitFor       string     `json:"fit_for"`       //适合季节/月份
	FitMonths    string     `json:"fit_months"`    //适合月份
	Budget       string     `json:"budget"`        //预算
	BudgetDetail string     `json:"budget_detail"` //预算明细
	BgPic        string     `json:"bg_pic"`        //背景图
	SceneNum     int        `json:"scene_num"`
	List         []*Journey `json:"list"`
}

func (t *RenderTravelJourney) MarshalBinary() (data []byte, err error) {
	return json.Marshal(t)
}

func (t *RenderTravelJourney) UnmarshalBinary(data []byte) error {
	return json.Unmarshal(data, t)
}

type JourneyHistory struct {
	JourneyData   *RenderTravelJourney
	PromptOptions *TravelPromptOptions
}

type FrontAuthToken struct {
	Nickname string `json:"nickname"`
	Avatar   string `json:"avatar"`
	UserId   uint   `json:"user_id"`
	Token    string `json:"token"`
	OpenId   string `json:"open_id"`
}

type Scenic struct {
	Id       uint    `json:"id"`
	ZoneId   uint    `json:"zone_id"`
	Name     string  `json:"name"`
	Lng      float64 `json:"lng"`
	Lat      float64 `json:"lat"`
	Address  string  `json:"address"`
	Tel      string  `json:"tel"`
	CostText string  `json:"cost_text"`
	Pic      string  `json:"pic"`
	Level    int     `json:"level"`
	State    int     `json:"state"`
}

// 旅客信息
type TravelPeople struct {
	Name         string          `json:"name"`          //姓名
	Phone        string          `json:"phone"`         //手机号
	IdType       constmap.IdType `json:"id_type"`       //证件类型
	IdNo         string          `json:"id_no"`         //证件号
	CustomerType int             `json:"customer_type"` //1成人 2儿童
}

// 会员权益内容
type VipBenefit struct {
	Code        constmap.VipBenefitCode `json:"code"`
	Name        string                  `json:"name"`
	Desc        string                  `json:"desc"`
	Icon        string                  `json:"icon"`
	Type        constmap.VipBenefitType `json:"type"`
	Discount    float64                 `json:"discount"`
	Times       int                     `json:"times"`
	RemainTimes int                     `json:"remain_times"`
}

// 用户会员信息
type UserVipInfo struct {
	UserType constmap.UserType `json:"user_type"`
	State    int               `json:"state"`
	VipName  string            `json:"vip_name"`
	VipIcon  string            `json:"vip_icon"`
	VipStart string            `json:"vip_start"`
	VipEnd   string            `json:"vip_end"`
	RealName string            `json:"real_name"`
}

// 会员内容
type VipConf struct {
	Id          uint         `json:"id"`
	Name        string       `json:"name"`
	Desc        string       `json:"desc"`
	Icon        string       `json:"icon"`
	MarketPrice uint         `json:"market_price"`
	SalePrice   uint         `json:"sale_price"`
	Benefit     []VipBenefit `json:"benefit"`
}

// 用户套餐情况
type UserPackageInfo struct {
	PackageId   uint   `json:"package_id"`
	PackageName string `json:"package_name"`
	ExpireAt    int64  `json:"expire_at"`
	Amount      int    `json:"amount"` //积分余额
}

type AccountLogExtra struct {
	PaymentId     uint
	UserPackageId uint
	AiReqid       string
}

// 会员vip记录扩展
type UserVipExtra struct {
	VipConfId uint
}
type HotelRoomCancelPolicy struct {
	DateFrom constmap.DateUnixStamp         `json:"date_from"`
	DateTo   constmap.DateUnixStamp         `json:"date_to"`
	Type     constmap.HotelCancelPolicyType `json:"type"`
	TypeDesc string                         `json:"type_desc"`
	Value    float64                        `json:"value"` //罚金
	Text     string                         `json:"text"`
}

func (o HotelRoomCancelPolicy) GetText(priceRate int64) string {
	var s strings.Builder
	s.WriteString(fmt.Sprintf("%s~%s", o.DateFrom.Format(constmap.DateFmtLongMinute), o.DateTo.Format(constmap.DateFmtLongMinute)))
	if o.Type == constmap.HotelCancelPolicyFree {
		s.WriteString("免费取消")
	} else if o.Type == constmap.HotelCancelPolicyFixedMoney {
		price := utils.CurrencyFloat2Int(o.Value)
		price = utils.GetOtaPrice(priceRate, price, true)
		s.WriteString(fmt.Sprintf("扣除%.2f元", utils.CurrencyInt2Float(price)))
	} else if o.Type == constmap.HotelCancelPolicyPercent {
		s.WriteString(fmt.Sprintf("按比例扣除%.2f", o.Value))
	} else if o.Type == constmap.HotelCancelPolicyFirstNight {
		s.WriteString("扣除" + o.Type.ToString())
	} else if o.Type == constmap.HotelCancelPolicyAll {
		s.WriteString("扣除" + o.Type.ToString())
	}
	return s.String()
}

type HotelRoomPriceDaily struct {
	Date      constmap.DateUnixStamp `json:"date"`
	SalePrice int64                  `json:"sale_price"`
	Quota     int                    `json:"quota"` //剩余数
	Valid     bool                   `json:"valid"` //是否可订
}
type HotelRoomPolicy struct {
	Id             string                  `json:"id"`              //政策id
	Name           string                  `json:"name"`            //政策名
	CanCancel      bool                    `json:"can_cancel"`      //是否可取消
	CancelRule     string                  `json:"cancel_rule"`     //取消规则
	CancelDesc     string                  `json:"cancel_desc"`     //取消规则政策描述
	InstantConfirm bool                    `json:"instant_confirm"` //是否立即确认
	ConfirmDesc    string                  `json:"confirm_desc"`    //即时确认文案
	SalesPrice     int64                   `json:"sales_price"`     //售价 （和平均售价一致）
	RemainRooms    int                     `json:"remain_rooms"`    //剩余房间数
	Tags           []string                `json:"tags"`            //政策标签
	MinAmount      int                     `json:"min_amount"`      //最小房间数限制
	MaxAmount      int                     `json:"max_amount"`      //最大房间数限制
	Person         int                     `json:"person"`          //房间最大入住人数
	Breakfast      string                  `json:"breakfast"`       //早餐类型描述
	TicketType     string                  `json:"ticket_type"`     //发票类型
	NeedIdCard     bool                    `json:"need_id_card"`    //是否需要证件
	NeedEnName     bool                    `json:"need_en_name"`    //是否需要英文名
	SuggestPrice   int64                   `json:"suggest_price"`   //建议售价
	PriceDaily     []HotelRoomPriceDaily   `json:"price_daily"`     //价格日历
	CancelPolicy   []HotelRoomCancelPolicy `json:"cancel_policy"`   //取消规则政策
}
type HotelRoom struct {
	Id      string            `json:"id"`   //房型ID
	Name    string            `json:"name"` //房型名
	Pic     string            `json:"pic"`
	BedDesc string            `json:"bed_desc"` //床型和房间描述
	Attrs   []string          `json:"attrs"`    //房型属性文字
	Policy  []HotelRoomPolicy `json:"policy"`   //房型政策
}
type TuanRefundRule struct {
	Day       int     `json:"day"`
	CanRefund int     `json:"can_refund"`
	Percent   float64 `json:"percent"` //退费比例0-1的小数
}
type OrderDetailExtTuan struct {
	AdultNum        int
	ChildrenNum     int
	Price           int
	SettlementPrice int
	CanRefund       int
	RefundRules     []TuanRefundRule
}
type OrderDetailExtTicket struct {
	ProductId    uint
	Date         int64
	OtaId        string
	OtaCode      string
	OtaSalePrice int64
	CanRefund    int
	CancelDay    int //距离游玩日期多少天可以直退
}
type OrderDetailExtHotel struct {
	ProductId uint
	Start     int64
	End       int64
	OtaId     string
	OtaCode   string
	RoomId    string
	PolicyId  string
	FeeItems  []SubOrderFeeItem
	Room      *HotelRoom
}

type OrderDetailRoom struct {
	RoomIndex int
	PeopleIdx int
	Refunded  bool //是否已退
}

type OtaRefundCheckResp struct {
	RefundOk       bool
	YbRefundAmount int64
	RefundAmount   int64
	PenaltyAmount  int64
}

type TravelProduct struct {
	ProductType constmap.ProductType `json:"product_type"`
	ProductId   uint                 `json:"product_id"`
	Num         int                  `json:"num"`        //购买数量
	Peoples     []TravelPeople       `json:"peoples"`    //出行人
	SalePrice   int64                `json:"sale_price"` //单价
	Extra       json.RawMessage      `json:"extra"`
}

func (o *TravelProduct) Check() error {
	if o.Num < 1 {
		return fmt.Errorf("购买数量必须大于0")
	}
	return nil
}

type TravelProductExtraTicket struct {
	Date  int64             `json:"date" `  //游玩日期
	OtaId string            `json:"ota_id"` //ota产品id
	DateT constmap.DateTime `json:"-"`
}

func (o *TravelProductExtraTicket) Check() error {
	if o.OtaId == "" {
		return fmt.Errorf("门票产品扩展信息错误")
	}
	if o.Date == 0 {
		return fmt.Errorf("请选择游玩日期")
	}
	o.DateT = constmap.DateTime{Time: time.Unix(o.Date, 0)}
	if o.DateT.IsZero() || o.DateT.Format(constmap.DateFmtShort) < time.Now().Format(constmap.DateFmtShort) {
		return fmt.Errorf("游玩日期错误，不能选择过去的日期")
	}
	return nil
}

type TravelProductExtraHotel struct {
	Start       int64             `json:"start" `        //开始时间
	End         int64             `json:"end"`           //结束时间
	OtaId       string            `json:"ota_id"`        //ota产品id
	OtaRoomId   string            `json:"ota_room_id"`   //ota房间id
	OtaPolicyId string            `json:"ota_policy_id"` //ota房型政策id
	StartT      constmap.DateTime `json:"-"`
	EndT        constmap.DateTime `json:"-"`
}

func (o *TravelProductExtraHotel) Check() error {
	if o.OtaId == "" || o.OtaPolicyId == "" || o.OtaRoomId == "" {
		return fmt.Errorf("酒店产品扩展信息错误")
	}
	if o.Start == 0 || o.End == 0 {
		return fmt.Errorf("请选择住宿时间")
	}
	o.StartT = constmap.DateTime{Time: time.Unix(o.Start, 0)}
	o.EndT = constmap.DateTime{Time: time.Unix(o.End, 0)}
	if o.StartT.IsZero() || o.EndT.IsZero() ||
		o.StartT.Format(constmap.DateFmtShort) < time.Now().Format(constmap.DateFmtShort) ||
		o.StartT.After(o.EndT.Time) {
		return fmt.Errorf("住宿时间错误，不能选择过去的日期")
	}
	return nil
}

type QueryHotelRoomPolicyDto struct {
	OtaId           string
	Start           int64
	End             int64
	LoadPolicyPrice bool
	PriceRate       int64
}

type TravelOrderPayResp struct {
	Err             error
	ThirdOrderState constmap.ThirdPayState //三方订单更新状态
}

type TravelPromptOptions struct {
	From          string                 `json:"from"`
	To            string                 `json:"to"`
	Interest      string                 `json:"interest"`      //兴趣偏好
	Strength      string                 `json:"strength"`      //强度
	Accommodation string                 `json:"accommodation"` //住宿
	Days          string                 `json:"days"`          //预期天数
	ExtraScenes   string                 `json:"extra_scenes"`  //想玩的景点
	TravelDate    string                 `json:"travel_date"`   //出行时间
	People        string                 `json:"people"`        //出行人
	Transport     string                 `json:"transport"`     //交通方式
	HowPlay       constmap.TravelHowPlay `json:"how_play"`      //您想怎么玩
	StartDate     int64                  `json:"start_date"`    //出发日期
}

type PlanTop struct {
	AiReqid   string `json:"ai_reqid"`
	Subject   string `json:"subject"`
	Subtitle  string `json:"subtitle"`
	Notice    string `json:"notice"`     //注意事项
	FitFor    string `json:"fit_for"`    //适合季节/月份
	FitMonths string `json:"fit_months"` //适合月份
	Poster    string `json:"poster"`     //行程海报
	UserId    uint   `json:"user_id"`
}

// 行程分段详情
type PlanSectionContent struct {
	Scenic            string
	Speciality        string
	PassingZones      []uint       //途经城市
	RecScenes         []uint       //推荐景点[废弃]
	RecAccommodations []uint       //推荐住宿[废弃]
	Timeline          []*JTimeline //路线轴
}

// API响应专用的行程分段详情（下划线格式）
type ApiPlanSectionContent struct {
	Scenic            string       `json:"scenic"`
	Speciality        string       `json:"speciality"`
	PassingZones      []uint       `json:"passing_zones"`      //途经城市
	RecScenes         []uint       `json:"rec_scenes"`         //推荐景点[废弃]
	RecAccommodations []uint       `json:"rec_accommodations"` //推荐住宿[废弃]
	Timeline          []*JTimeline `json:"timeline"`           //路线轴
}

type PlanSection struct {
	Title   string
	Subject string
	Content PlanSectionContent
}

// API响应专用的行程分段（下划线格式）
type ApiPlanSection struct {
	Title   string                `json:"title"`
	Subject string                `json:"subject"`
	Content ApiPlanSectionContent `json:"content"`
}

// 转换函数：PlanSectionContent -> ApiPlanSectionContent
func ConvertToApiPlanSectionContent(content PlanSectionContent) ApiPlanSectionContent {
	return ApiPlanSectionContent{
		Scenic:            content.Scenic,
		Speciality:        content.Speciality,
		PassingZones:      content.PassingZones,
		RecScenes:         content.RecScenes,
		RecAccommodations: content.RecAccommodations,
		Timeline:          content.Timeline,
	}
}

// 转换函数：PlanSection -> ApiPlanSection
func ConvertToApiPlanSection(section PlanSection) ApiPlanSection {
	return ApiPlanSection{
		Title:   section.Title,
		Subject: section.Subject,
		Content: ConvertToApiPlanSectionContent(section.Content),
	}
}

// 转换函数：[]PlanSection -> []ApiPlanSection
func ConvertToApiPlanSectionSlice(sections []PlanSection) []ApiPlanSection {
	if sections == nil {
		return nil
	}
	result := make([]ApiPlanSection, len(sections))
	for i, section := range sections {
		result[i] = ConvertToApiPlanSection(section)
	}
	return result
}

// 行程卡片信息
type TravelPlanTop struct {
	PlanLockData
	From      string `json:"from"`
	To        string `json:"to"`
	Days      int    `json:"days"`      //出行天数
	Transport string `json:"transport"` //交通方式
}

type SelectOption[T any] struct {
	Label string `json:"label"`
	Value T      `json:"value"`
}

type Holiday struct {
	Date     constmap.DateUnixStamp `json:"date"`
	WorkDay  int                    `json:"work_day"` //是否上班
	Festival string                 `json:"festival"` //节假日名称
}
type Zone struct {
	Id       uint    `json:"id"`
	Name     string  `json:"name"`
	ParentId uint    `json:"parent_id"`
	Level    int     `json:"level"`
	Children []*Zone `json:"children"`
}

// 用户任务日志扩展信息
type UserTaskLogExtra struct {
	TaskName             string
	CondType             constmap.TaskCond
	CondAmount           int
	CondTimes            int
	RewardType           constmap.TaskReward //奖励类型
	RewardAmount         int                 //领取的数额
	AccountTransactionNo string
}

type CanRefundOrder struct {
	OrderId      uint              `json:"order_id"`
	PayAmount    int64             `json:"pay_amount"`
	TotalAmount  int64             `json:"total_amount"`
	CreatedAt    int64             `json:"created_at"`
	ContactsName string            `json:"contacts_name"`
	ContactsTel  string            `json:"contacts_tel"`
	List         []CanRefundDetail `json:"list"`
}

// 可退款子订单
type CanRefundDetail struct {
	OrderDetailId     uint                  `json:"order_detail_id"`
	Quantity          int                   `json:"quantity"`
	CanRefundQuantity int                   `json:"can_refund_quantity"`
	OrderSubType      constmap.OrderSubType `json:"order_sub_type"`
	ProductType       constmap.ProductType  `json:"product_type"`
	ProductId         uint                  `json:"product_id"`
	SkuId             uint                  `json:"sku_id"`
	Pic               string                `json:"pic"`
	ProductName       string                `json:"product_name"`
	SkuName           string                `json:"sku_name"`
	SkuPrice          int64                 `json:"sku_price"`
	SkuRefundPrice    int64                 `json:"sku_refund_price"`
	DateStart         int64                 `json:"date_start"`
	DateEnd           int64                 `json:"date_end"`
	OtaPriceRate      int64                 `json:"-"`
}
type RefundTuan struct {
	CanRefund   int              `json:"can_refund"`
	RefundRules []TuanRefundRule `json:"refund_rules"`
}
type RefundTicket struct {
	Date      int64 `json:"date"`
	CanRefund int   `json:"can_refund"`
	CancelDay int   `json:"cancel_day"` //距离游玩日期多少天可以直退
}
type RefundHotelPolicy struct {
	CanCancel     bool                    `json:"can_cancel"`
	CancelRule    string                  `json:"cancel_rule"` //取消规则
	CancelDesc    string                  `json:"cancel_desc"` //取消规则政策描述
	CancelPolices []HotelRoomCancelPolicy `json:"cancel_polices"`
}
type RefundHotel struct {
	Name    string            `json:"name"` //房型名
	Pic     string            `json:"pic"`
	BedDesc string            `json:"bed_desc"` //床型和房间描述
	Attrs   []string          `json:"attrs"`    //房型属性文字
	Policy  RefundHotelPolicy `json:"policy"`
}

// sse流输出完毕时返回的data
type StreamWorkflowFinishData struct {
	ID          string                 `json:"id"`                     // 工作流执行唯一ID
	WorkflowID  string                 `json:"workflow_id"`            // 关联的工作流ID
	Status      string                 `json:"status"`                 // 执行状态（running/succeeded/failed/stopped）
	Outputs     map[string]interface{} `json:"outputs,omitempty"`      // 可选输出内容
	Error       string                 `json:"error,omitempty"`        // 可选错误原因
	ElapsedTime float64                `json:"elapsed_time,omitempty"` // 可选耗时（秒）
	TotalTokens int                    `json:"total_tokens,omitempty"` // 可选总使用tokens
	TotalSteps  int                    `json:"total_steps"`            // 总步数（默认0）
	CreatedAt   time.Time              `json:"created_at"`             // 开始时间
	FinishedAt  time.Time              `json:"finished_at"`            // 结束时间
}
type ScenicPrices struct {
	IsFree bool `json:"is_free"`
	Adult  struct {
		MonthlyPriceAvg float64 `json:"monthly_price_avg"`
		MonthlyPrices   []struct {
			Month int     `json:"month"`
			Price float64 `json:"price"`
		} `json:"monthly_prices"`
	} `json:"adult"`
	Child struct {
		Discount string `json:"discount"`
	} `json:"child"`
	Senior struct {
		Discount string `json:"discount"`
	} `json:"senior"`
}

// 游玩耗时
type ScenicPlayCosts struct {
	Standard float64 `json:"standard"`
	InDepth  float64 `json:"in_depth"`
}

// 首页行程推荐卡片
type PlanCard struct {
	AiReqid  string   `json:"ai_reqid"`
	Subject  string   `json:"subject"`
	Subtitle string   `json:"subtitle"`
	Tags     []string `json:"tags"`
	CostAvg  float64  `json:"cost_avg"` //预计费用/人
}

func (t *PlanCard) MarshalBinary() (data []byte, err error) {
	return json.Marshal(t)
}

func (t *PlanCard) UnmarshalBinary(data []byte) error {
	return json.Unmarshal(data, t)
}

type WaymapPoint [2]float64
type WaymapResp struct {
	Day      int           `json:"day"`
	Polyline []WaymapPoint `json:"polyline"`
	Distance float64       `json:"distance"` //单位：米
	Cost     float64       `json:"cost"`
}
type WaymapOut struct {
	Routes []WaymapResp `json:"routes"`
}
type PlanDetailReq struct {
	AiReqid        string  `form:"ai_reqid"`
	PlanId         uint    `form:"plan_id"`
	Lat            float64 `form:"lat"`
	Lng            float64 `form:"lng"`
	PolylineSample float64 //每段导航的采样率
	DisableCache   bool    //禁用地图缓存
}

// 卡片锁定时需要的字段
type PlanLockData struct {
	PromptOptions     *TravelPromptOptions `json:"prompt_options"`
	SceneNum          int                  `json:"scene_num"`
	IntegralCostState int                  `json:"integral_cost_state"`
	IntegralCost      int64                `json:"integral_cost"`
}

type PlanPdfPrimeScene struct {
	Pic       string                 `json:"pic"`        //封面图
	SceneId   uint                   `json:"scene_id"`   //景点ID
	SceneName string                 `json:"scene_name"` //景点名称
	SceneDesc string                 `json:"scene_desc"` //景点描述
	Tags      []string               `json:"tags"`       //标签
	Tips      []SelectOption[string] `json:"tips"`       //最佳游览时间、门票价、地铁站点等
}
type PlanPdfCaptureSpot struct {
	Pic      string `json:"pic"`
	Name     string `json:"name"`
	BestTime string `json:"best_time"` //最佳拍照时段
}
type PlanPdfBuildings struct {
	Subject   string   `json:"subject"`   //建筑分类主题
	Buildings []string `json:"buildings"` //建筑名称
}
type PlanPdfChildScene struct {
	Name     string `json:"name"`
	Desc     string `json:"desc"`
	TimeCost string `json:"time_cost"` //耗时
}

// 景区
type PlanPdfSceneDistrict struct {
	Subway           string                `json:"subway"`   //地铁线路
	Distance         string                `json:"distance"` //游玩里程文字
	Tags             []string              `json:"tags"`
	SuitPeoples      []string              `json:"suit_peoples"`       //适合人群
	WarningNotes     []string              `json:"warning_notes"`      //注意事项
	BestRoutes       []string              `json:"best_routes"`        //最佳游玩路线{格式：A->B(多少km)}
	BestCaptureSpots []*PlanPdfCaptureSpot `json:"best_capture_spots"` //最佳拍照位置
	BuildingGroups   []*PlanPdfBuildings   `json:"building_groups"`    //必看建筑
	TutorialPic      string                `json:"tutorial_pic"`       //导览图
	ChildScenes      []*PlanPdfChildScene  `json:"child_scenes"`       //景点详解
}
type PlanPdfHotel struct {
	HotelCost string `json:"hotel_cost"` //价格
	StarDesc  string `json:"star_desc"`  //星级描述
}
type PlanPdfTimelineFood struct {
	Name string   `json:"name"`
	Tips []string `json:"tips"` //推荐店铺、时间、人均等
}
type PlanPdfTimeline struct {
	Type         constmap.JTimelineType `json:"type"`
	ItemId       uint                   `json:"item_id"`
	Title        string                 `json:"title"`
	Time         string                 `json:"time"` //上午、下午、晚上
	Desc         string                 `json:"desc"`
	Pics         []string               `json:"pics"`          //1-3张图片(至少一张)
	TimeCost     string                 `json:"time_cost"`     //耗时
	BusinessArea string                 `json:"business_area"` //商圈
	LocationDesc string                 `json:"location_desc"` //模型返回的位置描述，用于后续通过bing生成封面图
	Scene        *PlanPdfSceneDistrict  `json:"scene"`
	Hotel        *PlanPdfHotel          `json:"hotel"`
	Foods        []*PlanPdfTimelineFood `json:"foods"` //美食推荐
}

type PlanPdfSection struct {
	SectionTitle string             `json:"section_title"` //标题
	Timelines    []*PlanPdfTimeline `json:"timelines"`
}

type PlanPdfZoneCulture struct {
	CulturePic   string `json:"culture_pic"`   //文化图片
	CultureName  string `json:"culture_name"`  //民俗文化名称
	CultureDesc  string `json:"culture_desc"`  //民俗文化描述
	TimePeriod   string `json:"time_period"`   //最佳时段
	BusinessArea string `json:"business_area"` //商圈
	Fee          string `json:"fee"`           //费用
}
type PlanPdfZoneFood struct {
	Pic   string `json:"pic"`
	Name  string `json:"name"`
	Shops string `json:"shops"` //推荐店家
	Fee   string `json:"fee"`   //费用
}

// 特产
type PlanPdfZoneSpecialty struct {
	Pic  string `json:"pic"`
	Name string `json:"name"`
	Fee  string `json:"fee"`
}
type PlanPdfZone struct {
	ZoneId      uint                    `json:"zone_id"`
	ZoneName    string                  `json:"zone_name"`
	Cultures    []*PlanPdfZoneCulture   `json:"cultures"`    //民俗文化
	Foods       []*PlanPdfZoneFood      `json:"foods"`       //美食
	Specialties []*PlanPdfZoneSpecialty `json:"specialties"` //特产
}
type PlanPdfPrimeZone struct {
	ZoneName          string `json:"zone_name"`          //主要城市名称
	ZoneDesc          string `json:"zone_desc"`          //地理位置
	Climate           string `json:"climate"`            //气候特点
	CultureBackground string `json:"culture_background"` //文化背景
}

// 行程PDF导出数据
type PlanPdfData struct {
	TopPic                string                `json:"top_pic"`        //顶部图片
	TopColor              string                `json:"top_color"`      //顶部文字颜色
	MapPic                string                `json:"map_pic"`        //地图图片
	Subject               string                `json:"subject"`        //标题
	Subtitle              string                `json:"subtitle"`       //副标题
	DayNum                int                   `json:"day_num"`        //行程天数
	SceneNum              int                   `json:"scene_num"`      //景点数量
	Transport             string                `json:"transport"`      //交通方式
	TotalDistance         string                `json:"total_distance"` //总里程文字描述
	Fee                   string                `json:"fee"`            //预计费用(元)/人
	Attentions            []string              `json:"attentions"`     //出行注意事项
	PrimeZone             PlanPdfPrimeZone      `json:"prime_zone"`     //主要城市信息
	Zones                 []*ZoneSpecialityData `json:"zones"`          //每个城市的美食、伴手礼、特产
	PrimeScenes           []PlanPdfPrimeScene   `json:"prime_scenes"`   //主要景点信息
	Sections              []*PlanPdfSection     `json:"sections"`       //每日信息
	Qrcode                string                `json:"qrcode"`         //小程序页面二维码base64图片
	CssStyle              string                `json:"-"`              //css样式，前端渲染用
	CostDetail            *PlanCost
	CostTotalSpecialities int64  `json:"cost_total_specialities"` //特产合计
	ConstTotalTraffic     int64  `json:"const_total_traffic"`     //合计交通费用
	ChartCostImage        string `json:"chart_cost_image"`        //预算图表图片
}

func (t *PlanPdfData) MarshalBinary() (data []byte, err error) {
	return json.Marshal(t)
}

func (t *PlanPdfData) UnmarshalBinary(data []byte) error {
	return json.Unmarshal(data, t)
}

type HotelSearchConditions struct {
	Lng        float64 `form:"lng" json:"lng"`
	Lat        float64 `form:"lat" json:"lat"`
	Radius     float64 `form:"radius" json:"radius"`           //搜索半径(单位:千米)，默认3公里
	BrandName  string  `form:"brand_name" json:"brand_name"`   //品牌名称
	Name       string  `form:"name" json:"name"`               //酒店名称
	PriceRange string  `form:"price_range" json:"price_range"` //价格范围，格式: 200-300
	Star       int     `form:"star" json:"star"`               //星级
	StarRange  string  `form:"star_range" json:"star_range"`   //星级范围，格式: 0-5
}
