package beans

import (
	"roadtrip-api/src/constmap"
	"strings"
)

type TplMsgWish struct {
	Success string `json:"success"` //已达成
	Close   string `json:"close"`   //关闭心愿单
	Join    string `json:"join"`    //同行人加入
}

// 模板消息配置
type TplMsgKeys struct {
	PlanPdf string     `json:"plan_pdf"` //生成PDF任务处理变动消息
	Wish    TplMsgWish `json:"wish"`     //心愿单
}

type PlanPdfDifyKeys struct {
	PrimeZone   string `json:"prime_zone"`   //主要城市、注意事项
	PrimeScenes string `json:"prime_scenes"` //特色景点
	Section     string `json:"section"`      //每日行程
	Zone        string `json:"zone"`         //城市特产、文化
}

type DifyKnowledgeKey struct {
	ApiKey  string `json:"api_key"`
	StoreId string `json:"store_id"` //知识库id
	DocId   string `json:"doc_id"`   //文档id
}

type DifyKnowledgeHotelKey struct {
	DifyKnowledgeKey
	DocZoneMap map[uint]string `json:"doc_zone_map"`
}

type DifyKnowledgeHotelScene struct {
	Id   uint   `json:"id"`
	Name string `json:"name"`
}

// 酒店知识库文档分段
type DifyKnowledgeHotel struct {
	HotelId      uint                      `json:"hotel_id"`
	Name         string                    `json:"name"`
	Lng          float64                   `json:"lng"`
	Lat          float64                   `json:"lat"`
	Pic          string                    `json:"pic"`
	ZoneId       uint                      `json:"zone_id"`
	ZoneName     string                    `json:"zone_name"`
	Address      string                    `json:"address"`
	Star         int                       `json:"star"`
	StarDesc     string                    `json:"star_desc"`
	AvgPrice     string                    `json:"avg_price"`
	Score        float64                   `json:"score"` //好评评分
	BusinessArea string                    `json:"business_area"`
	BrandId      uint                      `json:"brand_id"`
	BrandName    string                    `json:"brand_name"`
	GuestType    string                    `json:"guest_type"`    //客人类型
	RecReason    string                    `json:"rec_reason"`    //推荐理由
	NearbyScenes []DifyKnowledgeHotelScene `json:"nearby_scenes"` //附近景点
}

// 特产
type PlanCostZoneSpecialty struct {
	ZoneId uint   `json:"zone_id"`
	Pic    string `json:"pic"`
	Name   string `json:"name"`
	Fee    int64  `json:"fee"`
}

// 景点消费
type PlanCostZoneScenic struct {
	ZoneId  uint   `json:"zone_id"`
	SceneId uint   `json:"scene_id"`
	Name    string `json:"name"`
	Fee     int64  `json:"fee"` //景点消费/每人
	IsFree  bool   `json:"is_free"`
}

// 行程消费
type PlanCost struct {
	Total        int64                    `json:"total"`        //总消费
	Ticket       int64                    `json:"ticket"`       //门票总消费/每人
	Hotel        int64                    `json:"hotel"`        //住宿总消费/每人
	Highway      int64                    `json:"highway"`      //高速费
	Fuel         int64                    `json:"fuel"`         //油费
	Food         int64                    `json:"food"`         //餐饮/每人/天
	Distance     float64                  `json:"distance"`     //里程(单位:米)
	Specialities []*PlanCostZoneSpecialty `json:"specialities"` //特产
	Scenes       []*PlanCostZoneScenic    `json:"scenes"`       //景点门票
}

type ZoneSpecialityCulture struct {
	CulturePic   string  `json:"culture_pic"`   //文化图片
	CultureName  string  `json:"culture_name"`  //民俗文化名称
	CultureDesc  string  `json:"culture_desc"`  //民俗文化描述
	TimePeriod   string  `json:"time_period"`   //最佳时段
	BusinessArea string  `json:"business_area"` //商圈
	Fee          float64 `json:"fee"`           //费用(元)
	FeeCent      int64   `json:"fee_cent"`      //分单位的
}
type ZoneSpecialityFood struct {
	Pic     string  `json:"pic"`
	Name    string  `json:"name"`
	Shops   string  `json:"shops"`    //推荐店家
	Fee     float64 `json:"fee"`      //费用(元)
	FeeCent int64   `json:"fee_cent"` //分单位的
}

// 特产
type ZoneSpecialitySpecialty struct {
	Pic     string  `json:"pic"`
	Name    string  `json:"name"`
	Fee     float64 `json:"fee"`      //元单位
	FeeCent int64   `json:"fee_cent"` //分单位的
}
type ZoneSpecialityData struct {
	ZoneId    uint   `json:"zone_id"`
	ZoneName  string `json:"zone_name"`
	FoodsCost struct {
		Breakfast     float64 `json:"breakfast"`
		BreakfastCent int64   `json:"breakfast_cent"`
		Lunch         float64 `json:"lunch"`
		LunchCent     int64   `json:"lunch_cent"`
		Dinner        float64 `json:"dinner"`
		DinnerCent    int64   `json:"dinner_cent"`
	} `json:"foods_cost"` //早中晚餐饮消费
	Cultures    []*ZoneSpecialityCulture   `json:"cultures"`    //民俗文化
	Foods       []*ZoneSpecialityFood      `json:"foods"`       //美食
	Specialties []*ZoneSpecialitySpecialty `json:"specialties"` //特产
}

// 广告配置
type SysAdConfig struct {
	IntegralPop struct { //获取积分弹窗
		Type   string `json:"type"`
		AdId   string `json:"ad_id"`  //广告id
		Amount int64  `json:"amount"` //获取积分数量
	} `json:"integral_pop"`
}

type UserActivityReward struct {
	RewardType constmap.TaskReward `json:"reward_type"`
	Amount     int64               `json:"amount"`
}
type UserActivityExt struct {
	Rewards []UserActivityReward
}

type WeekPkRankListItem struct {
	UserId         uint   `json:"user_id"`
	PlanId         uint   `json:"plan_id"`
	ActivityPlanId uint   `json:"activity_plan_id"` //行程、活动关联id
	RankId         uint   `json:"rank_id"`          //本周排名榜id
	Rank           int    `json:"rank"`             //排名
	Score          int64  `json:"score"`            //得分
	Poster         string `json:"poster"`           //海报图片
	Nickname       string `json:"nickname"`         //昵称
	Avatar         string `json:"avatar"`           //头像
}
type WeekPKRankList struct {
	Week    int64                `json:"week"`
	WeekNum int                  `json:"week_num"` //活动开始后第几周
	Total   int64                `json:"total"`
	List    []WeekPkRankListItem `json:"list"`
	HasMore bool                 `json:"has_more"`
}

type BeWhere struct {
	Where strings.Builder
	Args  []any
}

type Banner struct {
	Id    uint   `json:"id"`
	Title string `json:"title"`
	Link  string `json:"link"`
	Pic   string `json:"pic"`
}

type WishHotZone struct {
	Id   uint   `json:"id"`
	Name string `json:"name"`
	Poi  string `json:"poi"`
}
type WishHotTag struct {
	Tag   string `json:"tag"`
	Count int64  `json:"count"`
}
type WishHotDayTag struct {
	Name  string `json:"name"`
	Count int64  `json:"count"`
}

type HotZone struct {
	Id   uint    `json:"id"`
	Name string  `json:"name"`
	Lng  float64 `json:"lng"`
	Lat  float64 `json:"lat"`
}

type WishComment struct {
	Id              uint           `json:"id"`
	ParentCommentId uint           `json:"parent_comment_id"`
	UserId          uint           `json:"user_id"`
	Nickname        string         `json:"nickname"`
	Avatar          string         `json:"avatar"`
	ReplyCommentId  uint           `json:"reply_comment_id"`
	ReplyUserId     uint           `json:"reply_user_id"`
	ReplyNickname   string         `json:"reply_nickname"`
	ReplyNum        int64          `json:"reply_num"`
	Replys          []*WishComment `json:"replys"`
}
