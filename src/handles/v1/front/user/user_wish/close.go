package user_wish

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/my_cache"
	"roadtrip-api/src/components/my_queue"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

// 关闭心愿
func Close(ctx *gin.Context) (any, error) {
	// 1. 参数定义和校验
	var in struct {
		WishId uint `form:"wish_id" binding:"required"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	// 2. 用户身份验证
	session, err := business.GetFrontLoginUser(ctx)
	if err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorNotLogin, "请先登录")
	}

	// 3. 获取数据库连接
	db := utils.GetDB(ctx)

	// 4. 并发控制
	mutexKey := fmt.Sprintf(constmap.RKMutex, "wishClose", fmt.Sprintf("%d:%d", session.UserId, in.WishId))
	unlocker, err := my_cache.Mutex(mutexKey, constmap.TimeDur1m)
	if err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorSystem, "操作过快，请稍后重试")
	}
	defer unlocker()

	// 5. 心愿单存在性和权限验证
	var wish models.Wish
	if err := db.Take(&wish, in.WishId).Error; err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "心愿单不存在")
	}

	// 6. 验证用户权限（是否为创建者）
	if wish.UserId != session.UserId {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "无权限操作此心愿单")
	}

	// 7. 状态验证（只有审核中、进行中状态的心愿单才能被关闭）
	if wish.State != constmap.WishStateWaitReview && wish.State != constmap.WishStateProcessing {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "当前状态的心愿单不允许关闭")
	}

	// 8. 执行关闭操作
	if err := db.Model(&wish).Updates(models.Wish{State: constmap.WishStateClosed}).Error; err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorSystem, "关闭失败，请稍后重试")
	}

	_ = my_queue.Light(constmap.EventWishClose, gin.H{
		"id":     in.WishId,
		"reason": "用户手动关闭",
	})

	// 9. 返回结果
	var out struct {
		WishId uint               `json:"wish_id"`
		State  constmap.WishState `json:"state"`
	}
	out.WishId = in.WishId
	out.State = wish.State

	return out, nil
}
