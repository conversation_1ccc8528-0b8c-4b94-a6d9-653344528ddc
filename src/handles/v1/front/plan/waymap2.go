package plan

import (
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/components/business/plan_biz"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/handles/v1/front/plan/internal/biz"
	"roadtrip-api/src/handles/v1/front/plan/internal/def"
	"roadtrip-api/src/utils"
)

// 行程地图
func WayMap2(ctx *gin.Context) (any, error) {
	var in def.PlanDetailReq

	if err := ctx.ShouldBind(&in); err != nil {
		return nil, err
	}
	if in.AiReqid == "" || in.Lat == 0 || in.Lng == 0 {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	rspDto, err := parseDetail(ctx, in)
	if err != nil {
		return nil, utils.NewError(err)
	}

	out, err := plan_biz.Waymap(ctx, in.PlanDetailReq, biz.GetWaymapPoi(rspDto))

	return out, err
}
