package wish

import (
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/business/like_biz"
	"roadtrip-api/src/components/business/user_biz"
	"roadtrip-api/src/components/business/wish_biz"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"time"

	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
)

func List(ctx *gin.Context) (any, error) {
	// 定义输入参数结构
	var in struct {
		UserId      uint  `form:"user_id"`      // 用户ID搜索
		State       int   `form:"state"`        // 心愿状态筛选，默认为0表示不筛选
		FollowState int   `form:"follow_state"` // 跟进状态筛选，默认为0表示不筛选
		OpenScope   int   `form:"open_scope"`   // 自动隐藏筛选，默认为0表示不筛选
		StartTime   int64 `form:"start_time"`   // 创建开始时间
		EndTime     int64 `form:"end_time"`     // 创建结束时间
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, err
	}

	db := utils.GetDB(ctx)
	page, pageSize := utils.GetPage(ctx)

	// 定义返回数据结构
	type item struct {
		Id            uint                    `json:"id"`
		UserId        uint                    `json:"user_id"`
		Nickname      string                  `json:"nickname"`
		Avatar        string                  `json:"avatar"`
		Title         string                  `json:"title"`
		OpenScope     int                     `json:"open_scope"`
		OpenScopeText string                  `json:"open_scope_text"`
		CreatedAt     int64                   `json:"created_at"`
		UpdatedAt     int64                   `json:"updated_at"`
		DateStr       string                  `json:"date_str"`
		MemberCount   int                     `json:"member_count"`
		Likes         int64                   `json:"likes"`
		BudgetType    constmap.WishBudgetType `json:"budget_type"` //预算类型
		Budget        string                  `json:"budget"`      //预算
		State         constmap.WishState      `json:"state"`
		StateText     string                  `json:"state_text"`
		RejectReason  string                  `json:"reject_reason"` //关闭原因
		CanClose      bool                    `json:"can_close"`     //是否可关闭
		CanFinish     bool                    `json:"can_finish"`    //可标记已去过
	}

	var data struct {
		Total int64  `json:"total"`
		List  []item `json:"list"`
	}

	// 构建查询条件
	query := db.Model(&models.Wish{})

	// 处理搜索条件
	if in.UserId > 0 {
		query = query.Where("user_id = ?", in.UserId)
	}

	if in.State > 0 {
		query = query.Where("state = ?", in.State)
	}

	if in.FollowState > 0 {
		query = query.Where("follow_state = ?", in.FollowState)
	}

	if in.OpenScope > 0 {
		query = query.Where("open_scope = ?", in.OpenScope)
	}

	if in.StartTime > 0 {
		query = query.Where("created_at >= ?", time.Unix(in.StartTime, 0))
	}

	if in.EndTime > 0 {
		query = query.Where("created_at <= ?", time.Unix(in.EndTime, 0))
	}

	// 获取总数
	query.Count(&data.Total)

	// 获取心愿单列表
	var wishes []models.Wish
	query.Order("id DESC").
		Limit(pageSize).
		Offset((page - 1) * pageSize).
		Find(&wishes)

	// 获取用户ID列表
	userIds := slice.Map(wishes, func(index int, wish models.Wish) uint {
		return wish.UserId
	})

	// 批量查询用户信息
	userMap := user_biz.LoadUsers(db, userIds)

	// 获取心愿单ID列表
	wishIds := slice.Map(wishes, func(index int, wish models.Wish) uint {
		return wish.ID
	})

	likeMap := like_biz.LoadLikes(db, constmap.LikeWish, wishIds)

	// 批量查询成员数量
	memberCountMap := make(map[uint]int)
	if len(wishIds) > 0 {
		var memberCounts []struct {
			WishId uint `gorm:"column:wish_id"`
			Count  int  `gorm:"column:count"`
		}
		db.Model(&models.WishMember{}).
			Select("wish_id, COUNT(*) as count").
			Where("wish_id IN ? AND state = ?", wishIds, constmap.WishMemberStateApproved).
			Group("wish_id").
			Find(&memberCounts)

		for _, mc := range memberCounts {
			memberCountMap[mc.WishId] = mc.Count
		}
	}

	// 组装返回数据
	data.List = slice.Map(wishes, func(index int, wish models.Wish) item {
		user, userExists := userMap[wish.UserId]

		return item{
			Id:            wish.ID,
			UserId:        wish.UserId,
			Nickname:      utils.If(userExists, user.Nickname, ""),
			Avatar:        utils.If(userExists, utils.AvatarUrl(user.Avatar), ""),
			Title:         wish.Title,
			OpenScope:     wish.OpenScope,
			OpenScopeText: utils.If(wish.OpenScope == constmap.Disable, "隐藏", "公开"),
			DateStr:       wish_biz.GetDepartReturnStr(wish.DepartDate, wish.ReturnDate),
			CreatedAt:     wish.CreatedAt.Unix(),
			UpdatedAt:     wish.UpdatedAt.Unix(),
			MemberCount:   memberCountMap[wish.ID],
			Likes:         likeMap[wish.ID],
			BudgetType:    wish.BudgetType,
			Budget:        wish.Budget,
			State:         wish.State,
			StateText:     business.WishStateAdminText(wish.State),
			RejectReason:  wish.RejectReason,
			CanClose:      wish.State == constmap.WishStateWaitReview || wish.State == constmap.WishStateProcessing,
			CanFinish:     wish.State == constmap.WishStateSuccess,
		}
	})

	return data, nil
}
