package wish

import (
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/components/my_queue"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

// Close 关闭心愿单（管理后台）
func Close(ctx *gin.Context) (any, error) {
	var in struct {
		WishId uint   `form:"wish_id" binding:"required"` // 心愿单ID
		Reason string `form:"reason"`                     // 关闭原因（可选）
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	db := utils.GetDB(ctx)

	// 查询要关闭的心愿单
	var wish models.Wish
	if err := db.Take(&wish, in.WishId).Error; err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "心愿单不存在")
	}

	// 验证状态（只有待审核和进行中的心愿单才能被关闭）
	if wish.State != constmap.WishStateWaitReview && wish.State != constmap.WishStateProcessing {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "当前状态的心愿单不可关闭")
	}

	// 执行关闭操作，使用WHERE条件确保只有待审核和进行中的心愿单才能关闭
	result := db.Model(&models.Wish{}).
		Where("id = ? AND state IN (?)",
			in.WishId,
			[]constmap.WishState{
				constmap.WishStateWaitReview,
				constmap.WishStateProcessing,
			}).
		Updates(models.Wish{
			State:        constmap.WishStateClosed,
			RejectReason: in.Reason, // 保存关闭原因
		})
	if result.Error != nil {
		return nil, utils.NewErrorStr(constmap.ErrorSystem, "关闭失败，请稍后重试")
	}
	if result.RowsAffected == 0 {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "心愿单状态已改变，无法关闭")
	}

	// 触发关闭事件
	_ = my_queue.Light(constmap.EventWishClose, gin.H{
		"id":     wish.ID,
		"reason": utils.If(in.Reason != "", in.Reason, "管理员关闭"),
	})

	var out struct {
		WishId uint               `json:"wish_id"`
		State  constmap.WishState `json:"state"`
	}
	out.WishId = wish.ID
	out.State = constmap.WishStateClosed // 返回更新后的状态

	return out, nil
}
