package wish_biz

import (
	"fmt"
	"github.com/duke-git/lancet/v2/maputil"
	"github.com/duke-git/lancet/v2/slice"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/my_cache"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

// 批量加载回复，每条评论查询前3条回复，前置缓存
func LoadReplysCc(db *gorm.DB, addCond func(*gorm.DB) *gorm.DB, commentIds []uint) map[uint][]*beans.WishComment {
	ret := make(map[uint][]*beans.WishComment)
	if len(commentIds) == 0 {
		return ret
	}
	ckeys := make(map[string]uint)
	slice.ForEach(commentIds, func(index int, item uint) {
		ckeys[fmt.Sprintf(constmap.RKWishCommentReplys, item)] = item
	})

	if commentReplys, err := my_cache.MGetSet(ckeys, func(missKeys map[string]uint) (map[string]*utils.Marshaller[[]*beans.WishComment], error) {
		ret1 := make(map[string]*utils.Marshaller[[]*beans.WishComment])
		// 后续有接口响应问题可将循环查询改为并发查询
		maputil.ForEach(missKeys, func(key string, commentId uint) {
			var list []*models.WishComment
			q := db.Where(models.WishComment{ParentCommentId: commentId})
			q = addCond(q)
			q.Order("id ASC").Limit(3).Find(&list)
			vlist := make([]*beans.WishComment, 0)
			slice.ForEach(list, func(index int, cmt *models.WishComment) {
				vlist = append(vlist, &beans.WishComment{
					Id:              cmt.ID,
					ParentCommentId: cmt.ParentCommentId,
					UserId:          cmt.UserId,
					ReplyCommentId:  cmt.ReplyCommentId,
					ReplyUserId:     cmt.ReplyUserId,
					Replys:          make([]*beans.WishComment, 0),
				})
			})
			ret1[key] = &utils.Marshaller[[]*beans.WishComment]{
				Data: &vlist,
			}
		})
		return ret1, nil
	}, constmap.TimeDur1d); err == nil {
		maputil.ForEach(commentReplys, func(key string, value *utils.Marshaller[[]*beans.WishComment]) {
			if commentId, ok := ckeys[key]; ok {
				ret[commentId] = *value.Data
			}
		})
	} else {
		my_logger.Errorf("LoadReplysCc", zap.Error(err))
	}
	return ret
}
