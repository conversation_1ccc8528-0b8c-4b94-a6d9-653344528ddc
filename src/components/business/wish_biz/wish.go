package wish_biz

import (
	"fmt"
	"gorm.io/gorm"
	"regexp"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/business/zone_biz"
	"roadtrip-api/src/components/es"
	"roadtrip-api/src/components/my_cache"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"strings"
	"time"

	"github.com/duke-git/lancet/v2/slice"
	"github.com/duke-git/lancet/v2/strutil"
)

func GetHotZonesCc(db *gorm.DB) ([]beans.WishHotZone, error) {
	ckey := fmt.Sprintf(constmap.RKWishTop, "zone")
	v := &utils.Marshaller[[]beans.WishHotZone]{}
	if my_cache.Get(ckey, v) {
		ret := *v.Data
		return utils.If(len(ret) > 0, ret, make([]beans.WishHotZone, 0)), nil
	}

	// 从ES查询热门目的地城市
	query := map[string]any{
		"size": 0,
		"query": map[string]any{
			"bool": map[string]any{
				"must": []map[string]any{
					{
						"terms": map[string]any{
							"state": []constmap.WishState{constmap.WishStateSuccess, constmap.WishStateProcessing},
						},
					},
					{
						"term": map[string]any{
							"open_scope": constmap.Enable,
						},
					},
				},
			},
		},
		"aggs": map[string]any{
			"hot_zones": map[string]any{
				"terms": map[string]any{
					"field": "to_zone_id",
					"size":  10,
					"order": map[string]any{
						"_count": "desc",
					},
				},
			},
		},
	}

	// 执行ES聚合查询
	var result struct {
		Aggregations struct {
			HotZones struct {
				Buckets []struct {
					Key      uint  `json:"key"`
					DocCount int64 `json:"doc_count"`
				} `json:"buckets"`
			} `json:"hot_zones"`
		} `json:"aggregations"`
	}

	if err := es.SearchWithAggregations(constmap.EsIndexWish, query, &result); err != nil {
		return make([]beans.WishHotZone, 0), err
	}

	// 获取城市名称
	var zoneIds []uint
	for _, bucket := range result.Aggregations.HotZones.Buckets {
		zoneIds = append(zoneIds, bucket.Key)
	}

	// 构建结果
	zoneMap := zone_biz.NewZoneBiz().GetZoneMap(db, zoneIds)

	var hotZones = make([]beans.WishHotZone, 0)
	for _, bucket := range result.Aggregations.HotZones.Buckets {
		zone := zoneMap[bucket.Key]
		zoneName := zone.Name
		if zoneName == "" {
			zoneName = "未知城市"
		}
		hotZones = append(hotZones, beans.WishHotZone{
			Id:   bucket.Key,
			Name: zoneName,
			Poi:  utils.JoinPoi(zone.Lng, zone.Lat),
		})
	}

	// 缓存结果
	v.Data = &hotZones
	if err := my_cache.Set(ckey, v, constmap.TimeDur1d+utils.RandTime(time.Hour)); err != nil {
		// 缓存失败不影响返回结果，只记录错误
		fmt.Printf("缓存热门城市数据失败: %v\n", err)
	}

	return hotZones, nil
}

func GetHotTagsCc() ([]beans.WishHotTag, error) {
	ckey := fmt.Sprintf(constmap.RKWishTop, "tag")
	v := &utils.Marshaller[[]beans.WishHotTag]{}
	if my_cache.Get(ckey, v) {
		ret := *v.Data
		return utils.If(len(ret) > 0, ret, make([]beans.WishHotTag, 0)), nil
	}

	// 从ES查询热门标签
	query := map[string]any{
		"size": 0,
		"query": map[string]any{
			"bool": map[string]any{
				"must": []map[string]any{
					{
						"terms": map[string]any{
							"state": []constmap.WishState{constmap.WishStateSuccess, constmap.WishStateProcessing},
						},
					},
					{
						"term": map[string]any{
							"open_scope": constmap.Enable,
						},
					},
				},
			},
		},
		"aggs": map[string]any{
			"hot_tags": map[string]any{
				"terms": map[string]any{
					"field": "tags",
					"size":  5,
					"order": map[string]any{
						"_count": "desc",
					},
				},
			},
		},
	}

	// 执行ES聚合查询
	var result struct {
		Aggregations struct {
			HotTags struct {
				Buckets []struct {
					Key      string `json:"key"`
					DocCount int64  `json:"doc_count"`
				} `json:"buckets"`
			} `json:"hot_tags"`
		} `json:"aggregations"`
	}

	if err := es.SearchWithAggregations(constmap.EsIndexWish, query, &result); err != nil {
		return make([]beans.WishHotTag, 0), err
	}

	// 构建结果
	var hotTags = make([]beans.WishHotTag, 0)
	for _, bucket := range result.Aggregations.HotTags.Buckets {
		hotTags = append(hotTags, beans.WishHotTag{
			Tag:   bucket.Key,
			Count: bucket.DocCount,
		})
	}

	// 缓存结果
	v.Data = &hotTags
	if err := my_cache.Set(ckey, v, constmap.TimeDur1d+utils.RandTime(time.Hour)); err != nil {
		// 缓存失败不影响返回结果，只记录错误
		fmt.Printf("缓存热门标签数据失败: %v\n", err)
	}

	return hotTags, nil
}

func GetHotDayTagsCc() ([]beans.WishHotDayTag, error) {
	ckey := fmt.Sprintf(constmap.RKWishTop, "dayTag")
	v := &utils.Marshaller[[]beans.WishHotDayTag]{}
	if my_cache.Get(ckey, v) {
		ret := *v.Data
		return utils.If(len(ret) > 0, ret, make([]beans.WishHotDayTag, 0)), nil
	}

	// 从ES查询热门节假日
	query := map[string]any{
		"size": 0,
		"query": map[string]any{
			"bool": map[string]any{
				"must": []map[string]any{
					{
						"terms": map[string]any{
							"state": []constmap.WishState{constmap.WishStateSuccess, constmap.WishStateProcessing},
						},
					},
					{
						"term": map[string]any{
							"open_scope": constmap.Enable,
						},
					},
				},
			},
		},
		"aggs": map[string]any{
			"hot_holidays": map[string]any{
				"terms": map[string]any{
					"field": "day_tags",
					"size":  5,
					"order": map[string]any{
						"_count": "desc",
					},
				},
			},
		},
	}

	// 执行ES聚合查询
	var result struct {
		Aggregations struct {
			HotHolidays struct {
				Buckets []struct {
					Key      string `json:"key"`
					DocCount int64  `json:"doc_count"`
				} `json:"buckets"`
			} `json:"hot_holidays"`
		} `json:"aggregations"`
	}

	if err := es.SearchWithAggregations(constmap.EsIndexWish, query, &result); err != nil {
		return make([]beans.WishHotDayTag, 0), err
	}

	// 构建结果
	var hotHolidays = make([]beans.WishHotDayTag, 0)
	for _, bucket := range result.Aggregations.HotHolidays.Buckets {
		hotHolidays = append(hotHolidays, beans.WishHotDayTag{
			Name:  bucket.Key,
			Count: bucket.DocCount,
		})
	}

	// 缓存结果
	v.Data = &hotHolidays
	if err := my_cache.Set(ckey, v, constmap.TimeDur1d+utils.RandTime(time.Hour)); err != nil {
		// 缓存失败不影响返回结果，只记录错误
		fmt.Printf("缓存热门节假日数据失败: %v\n", err)
	}

	return hotHolidays, nil
}

// LoadWishes 批量查询wish数据，类似user_biz.LoadUsers
func LoadWishes(db *gorm.DB, wishIds []uint) map[uint]*models.Wish {
	if len(wishIds) == 0 {
		return map[uint]*models.Wish{}
	}

	var wishes []*models.Wish
	db.Find(&wishes, wishIds)

	return slice.KeyBy(wishes, func(wish *models.Wish) uint {
		return wish.ID
	})
}

// LoadWishTags 批量查询标签数据
func LoadWishTags(db *gorm.DB, tagIds []uint) map[uint]*models.WishTag {
	if len(tagIds) == 0 {
		return map[uint]*models.WishTag{}
	}

	var tags []*models.WishTag
	db.Find(&tags, tagIds)

	return slice.KeyBy(tags, func(tag *models.WishTag) uint {
		return tag.ID
	})
}

func ParseDepartReturn(departDate, returnDate string) []time.Time {
	var start time.Time
	var end time.Time
	if ok, _ := regexp.MatchString("^\\d{4}-\\d{2}$", departDate); ok {
		if tm, err := time.Parse(constmap.DateFmtMonthFull, departDate); err == nil {
			start = tm
		}
	} else if !strutil.IsBlank(departDate) && !strutil.IsBlank(returnDate) {
		start, _ = time.Parse(constmap.DateFmtLong, departDate)
		end, _ = time.Parse(constmap.DateFmtLong, returnDate)
	}
	ret := make([]time.Time, 0)
	if !start.IsZero() && !end.IsZero() {
		ret = append(ret, start, end)
	} else if !start.IsZero() {
		ret = append(ret, start)
	}
	return ret
}
func GetDepartReturnStr(departDate, returnDate string) string {
	var dateStr string
	times := ParseDepartReturn(departDate, returnDate)
	switch len(times) {
	default:
		dateStr = "待定"
	case 1:
		dateStr = departDate
	case 2:
		dateStr = fmt.Sprintf("%s(%d天)", times[0].Format(constmap.DateFmtLongDashed), 1+int(times[1].Sub(times[0]).Hours()/24))
	}
	return dateStr
}

// 根据心愿单信息生成AI对话提示语
func BuildWishPrompt(wish *models.Wish, todos []models.WishTodo) string {
	var prompt strings.Builder

	// 心愿标题
	if !strutil.IsBlank(wish.Title) {
		prompt.WriteString(fmt.Sprintf("心愿标题：%s\n", wish.Title))
	}

	// 出行路线
	if !strutil.IsBlank(wish.From) && !strutil.IsBlank(wish.To) {
		prompt.WriteString(fmt.Sprintf("出行路线：从%s到%s\n", wish.From, wish.To))
	} else if !strutil.IsBlank(wish.To) {
		prompt.WriteString(fmt.Sprintf("目的地：%s\n", wish.To))
	}

	// 出行时间
	if !strutil.IsBlank(wish.DepartDate) && !strutil.IsBlank(wish.ReturnDate) {
		prompt.WriteString(fmt.Sprintf("出行时间：%s~%s\n", wish.DepartDate, wish.ReturnDate))
	} else if !strutil.IsBlank(wish.DepartDate) {
		prompt.WriteString(fmt.Sprintf("出行时间：%s\n", wish.DepartDate))
	} else {
		prompt.WriteString("出行时间：灵活安排\n")
	}

	// 出行人数
	if wish.TotalPeople > 0 {
		prompt.WriteString(fmt.Sprintf("出行人数：%d人\n", wish.TotalPeople))
	}

	// 预算安排
	if !strutil.IsBlank(wish.Budget) {
		budgetTypeText := "整体预算"
		if wish.BudgetType == constmap.WishBudgetSingle {
			budgetTypeText = "人均预算"
		}
		prompt.WriteString(fmt.Sprintf("预算安排：%s %s\n", budgetTypeText, wish.Budget))
	}

	// 心愿描述
	if !strutil.IsBlank(wish.WishDesc) {
		prompt.WriteString(fmt.Sprintf("心愿描述：%s\n", wish.WishDesc))
	}

	// 必做事项
	if len(todos) > 0 {
		prompt.WriteString("行程计划：\n")
		for i, todo := range todos {
			mustText := ""
			if todo.IsMust == constmap.Enable {
				mustText = "【必做】"
			}
			prompt.WriteString(fmt.Sprintf("  %d. %s%s\n", i+1, mustText, todo.Todo))
		}
	}

	// 成员要求
	if !strutil.IsBlank(wish.MemberDesc) {
		prompt.WriteString(fmt.Sprintf("成员要求：%s\n", wish.MemberDesc))
	}

	return prompt.String()
}
