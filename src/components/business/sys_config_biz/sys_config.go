package sys_config_biz

import (
	"encoding/json"
	"fmt"
	"github.com/duke-git/lancet/v2/convertor"
	"github.com/pkg/errors"
	"gorm.io/gorm"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/my_cache"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
)

func GetConfig(db *gorm.DB, key string) (any, error) {

	var err error
	ckey := fmt.Sprintf(constmap.RKSysConfig, key)

	var value string
	if !my_cache.Get(ckey, &value) {
		var sysConfig models.SysConfig
		if err = db.Where(models.SysConfig{
			Key: key,
		}).Take(&sysConfig).Error; err != nil {
			return nil, err
		}
		value = sysConfig.Value
		_ = my_cache.Set(ckey, value, constmap.TimeDur30d)
	}

	switch key {
	default:
		return value, nil
	case constmap.SysConfigBindInviteCodeAmount,
		constmap.SysConfigGeneratePlanPdfCostAmount,
		constmap.SysConfigPlanCostAmount,
		constmap.SysConfigReplanCostAmount:
		return convertor.ToInt(value)
	case constmap.SysConfigTplMsgKeys:
		var b = new(beans.TplMsgKeys)
		err = json.Unmarshal([]byte(value), &b)
		return b, errors.Wrap(err, "Unmarshal TplMsgKeys")
	case constmap.SysConfigDifyPlanPdfKey:
		var b = new(beans.PlanPdfDifyKeys)
		err = json.Unmarshal([]byte(value), &b)
		return b, errors.Wrap(err, "Unmarshal PlanPdfDifyKeys")
	case constmap.SysConfigDifyKnowledgeHotelKey:
		var b = new(beans.DifyKnowledgeHotelKey)
		err = json.Unmarshal([]byte(value), &b)
		if b.DocZoneMap == nil {
			b.DocZoneMap = make(map[uint]string)
		}
		return b, errors.Wrap(err, "Unmarshal DifyKnowledgeHotelKey")
	case constmap.SysConfigAdConfig:
		var b = new(beans.SysAdConfig)
		err = json.Unmarshal([]byte(value), &b)
		return b, errors.Wrap(err, "Unmarshal DifyKnowledgeHotelKey")
	}
}

func ClearCache(key string) {
	my_cache.Remove(fmt.Sprintf(constmap.RKSysConfig, key))
}
